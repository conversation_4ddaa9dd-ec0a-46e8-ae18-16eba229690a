#!/usr/bin/env python3
"""
migrate_nics.py

Migrate or validate GCP VM NICs based on Excel input.
Features:
  - Movegroup-based selection
  - Dry-run mode
  - Parallel execution
  - Always restart after NIC migration
  - Separate validate-only mode
  - Structured logging
"""

import argparse
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, Optional, List

import pandas as pd
import google.auth
from googleapiclient import discovery
from googleapiclient.errors import HttpError
from tqdm import tqdm

# ----------------------------------------------------------------------
# Config
# ----------------------------------------------------------------------
POLL_INTERVAL = 5
OP_TIMEOUT = 600
STOP_TIMEOUT = 600

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)-8s | %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger("migrate-nics")

# ----------------------------------------------------------------------
# Helpers
# ----------------------------------------------------------------------
def zone_to_region(zone: str) -> str:
    return "-".join(zone.split("-")[:2])

def build_network(host_project: Optional[str], network: str) -> str:
    return f"projects/{host_project}/global/networks/{network}" if host_project else f"global/networks/{network}"

def build_subnetwork(project: str, region: str, subnet: str) -> str:
    return f"projects/{project}/regions/{region}/subnetworks/{subnet}"

def wait_for_zone_op(compute, project: str, zone: str, op_name: str, timeout: int = OP_TIMEOUT) -> None:
    start = time.time()
    while True:
        result = compute.zoneOperations().get(project=project, zone=zone, operation=op_name).execute()
        if result.get("status") == "DONE":
            if "error" in result:
                raise RuntimeError(result["error"])
            return
        if time.time() - start > timeout:
            raise TimeoutError(f"Operation {op_name} timed out")
        time.sleep(POLL_INTERVAL)

def wait_for_instance_status(compute, project: str, zone: str, instance: str, desired: str, timeout: int = STOP_TIMEOUT):
    start = time.time()
    while True:
        inst = compute.instances().get(project=project, zone=zone, instance=instance).execute()
        if inst.get("status") == desired:
            return inst
        if time.time() - start > timeout:
            raise TimeoutError(f"Timeout waiting for {instance} to be {desired}")
        time.sleep(POLL_INTERVAL)

# ----------------------------------------------------------------------
# Core functions
# ----------------------------------------------------------------------
def migrate_instance(row: Dict[str, Any], compute, dry_run: bool) -> Dict[str, Any]:
    """Stop → update NIC → start → validate"""
    proj, inst, zone, nic_name = map(lambda k: str(row.get(k, "")).strip(),
                                     ["ProjectID", "Name", "Location", "NicName"])
    t_vpc, t_subnet, t_ip = map(lambda k: str(row.get(k, "")).strip() or None,
                                ["TargetVPC", "TargetSubnet", "TargetIP"])
    t_proj = str(row.get("TargetProjectID") or proj).strip()
    t_host = str(row.get("TargetHostProject", "")).strip() or None

    if not all([proj, inst, zone, nic_name, t_vpc, t_subnet]):
        return {"instance": inst, "project": proj, "ok": False, "reason": "Missing required fields"}

    region = zone_to_region(zone)
    network = build_network(t_host, t_vpc)
    subnet = build_subnetwork(t_proj, region, t_subnet)

    logger.info("[%s] Plan: project=%s zone=%s nic=%s → %s/%s (ip=%s)",
                inst, proj, zone, nic_name, network, subnet, t_ip or "auto")

    if dry_run:
        return {"instance": inst, "project": proj, "ok": True, "dry_run": True}

    try:
        # Stop
        status = compute.instances().get(project=proj, zone=zone, instance=inst).execute().get("status")
        if status != "TERMINATED":
            logger.info("[%s] Stopping instance", inst)
            op = compute.instances().stop(project=proj, zone=zone, instance=inst).execute()
            wait_for_zone_op(compute, proj, zone, op["name"])
            wait_for_instance_status(compute, proj, zone, inst, "TERMINATED")
            logger.info("[%s] Stopped", inst)

        # Fingerprint
        inst_data = compute.instances().get(project=proj, zone=zone, instance=inst).execute()
        nic = next((n for n in inst_data["networkInterfaces"] if n["name"] == nic_name), None)
        if not nic:
            return {"instance": inst, "project": proj, "ok": False, "reason": f"NIC {nic_name} not found"}
        fp = nic["fingerprint"]

        # Update NIC
        body = {"network": network, "subnetwork": subnet, "fingerprint": fp}
        if t_ip:
            body["networkIP"] = t_ip
        logger.info("[%s] Updating NIC %s", inst, nic_name)
        op = compute.instances().updateNetworkInterface(
            project=proj, zone=zone, instance=inst, networkInterface=nic_name, body=body
        ).execute()
        wait_for_zone_op(compute, proj, zone, op["name"])
        logger.info("[%s] NIC updated", inst)

        # Start
        logger.info("[%s] Starting instance", inst)
        op = compute.instances().start(project=proj, zone=zone, instance=inst).execute()
        wait_for_zone_op(compute, proj, zone, op["name"])
        logger.info("[%s] Started", inst)

        # Validate
        return validate_instance(row, compute)

    except HttpError as e:
        logger.error("[%s] API error: %s", inst, e)
        return {"instance": inst, "project": proj, "ok": False, "reason": str(e)}
    except Exception as e:
        logger.exception("[%s] Migration failed", inst)
        return {"instance": inst, "project": proj, "ok": False, "reason": str(e)}

def validate_instance(row: Dict[str, Any], compute) -> Dict[str, Any]:
    """Check NIC network/subnet/ip against targets"""
    proj, inst, zone, nic_name = map(lambda k: str(row.get(k, "")).strip(),
                                     ["ProjectID", "Name", "Location", "NicName"])
    t_vpc, t_subnet, t_ip = map(lambda k: str(row.get(k, "")).strip() or None,
                                ["TargetVPC", "TargetSubnet", "TargetIP"])
    t_proj = str(row.get("TargetProjectID") or proj).strip()
    t_host = str(row.get("TargetHostProject", "")).strip() or None

    region = zone_to_region(zone)
    network = build_network(t_host, t_vpc)
    subnet = build_subnetwork(t_proj, region, t_subnet)

    try:
        inst_data = compute.instances().get(project=proj, zone=zone, instance=inst).execute()
        nic = next((n for n in inst_data["networkInterfaces"] if n["name"] == nic_name), None)
        if not nic:
            return {"instance": inst, "project": proj, "ok": False, "reason": "NIC missing"}

        issues: List[str] = []
        if network not in nic.get("network", ""):
            issues.append(f"Network mismatch (expected {network}, got {nic['network']})")
        if subnet not in nic.get("subnetwork", ""):
            issues.append(f"Subnet mismatch (expected {subnet}, got {nic['subnetwork']})")
        if t_ip and nic.get("networkIP") != t_ip:
            issues.append(f"IP mismatch (expected {t_ip}, got {nic['networkIP']})")

        if issues:
            logger.warning("[%s] Validation issues: %s", inst, issues)
            return {"instance": inst, "project": proj, "ok": False, "issues": issues}
        logger.info("[%s] Validation OK", inst)
        return {"instance": inst, "project": proj, "ok": True}

    except Exception as e:
        logger.error("[%s] Validation failed: %s", inst, e)
        return {"instance": inst, "project": proj, "ok": False, "reason": str(e)}

# ----------------------------------------------------------------------
# Runner
# ----------------------------------------------------------------------
def process_sheet(sheet: str, movegroup: str, workers: int, dry_run: bool, validate_only: bool):
    df = pd.read_excel(sheet, engine="openpyxl")
    if movegroup:
        df = df[df.get("TargetMovegroup", "") == movegroup]
        logger.info("Selected %d rows for movegroup '%s'", len(df), movegroup)

    creds, _ = google.auth.default()
    compute = discovery.build("compute", "v1", credentials=creds, cache_discovery=False)

    results = []
    with ThreadPoolExecutor(max_workers=workers) as pool:
        tasks = {
            pool.submit(validate_instance if validate_only else migrate_instance, row.to_dict(), compute, dry_run if not validate_only else None): row["Name"]
            for _, row in df.iterrows()
        }
        for fut in tqdm(as_completed(tasks), total=len(tasks), unit="vm", desc="Processing"):
            results.append(fut.result())
    return results

# ----------------------------------------------------------------------
# CLI
# ----------------------------------------------------------------------
def main():
    p = argparse.ArgumentParser(description="Migrate/Validate GCP VM NICs via Excel sheet")
    p.add_argument("--sheet", "-s", required=True, help="Excel file path")
    p.add_argument("--movegroup", "-m", default="", help="TargetMovegroup filter")
    p.add_argument("--workers", "-w", type=int, default=4, help="Parallel workers")
    p.add_argument("--dry-run", action="store_true", help="Dry-run migration")
    p.add_argument("--validate-only", action="store_true", help="Validation mode only")
    args = p.parse_args()

    mode = "validate" if args.validate_only else ("dry-run" if args.dry_run else "migrate")
    logger.info("Starting in %s mode | sheet=%s | movegroup=%s | workers=%d",
                mode, args.sheet, args.movegroup or "<ALL>", args.workers)

    results = process_sheet(args.sheet, args.movegroup, args.workers, args.dry_run, args.validate_only)
    ok = sum(1 for r in results if r.get("ok"))
    fail = len(results) - ok
    logger.info("Completed: %d success, %d failed, %d total", ok, fail, len(results))

if __name__ == "__main__":
    main()
